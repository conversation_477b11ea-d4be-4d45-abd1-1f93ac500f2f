import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';

const CountryOutlineBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const mountedRef = useRef<boolean>(false);

  // 粒子系统相关引用
  const particleSystemRef = useRef<THREE.Points | null>(null);
  const connectionLinesRef = useRef<THREE.Group | null>(null);
  const flowingParticlesRef = useRef<THREE.Group | null>(null);

  useEffect(() => {
    if (!containerRef.current || mountedRef.current) return;

    mountedRef.current = true;
    console.log('CountryOutlineBackground: 开始初始化');

    // 检查THREE.js是否可用
    if (typeof THREE === 'undefined') {
      console.error('THREE.js library is not loaded');
      return;
    }

    console.log('THREE.js 版本:', THREE.REVISION);

    // 获取容器尺寸
    const containerRect = containerRef.current.getBoundingClientRect();
    const width = containerRect.width || window.innerWidth;
    const height = containerRect.height || window.innerHeight;

    // 场景设置
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      premultipliedAlpha: false
    });

    // 配置渲染器
    renderer.setSize(width, height);
    renderer.setClearColor(0x000000, 0); // 完全透明背景
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比以提高性能

    // 设置canvas样式
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.pointerEvents = 'none';
    renderer.domElement.style.zIndex = '1';

    containerRef.current.appendChild(renderer.domElement);
    console.log('渲染器初始化完成, 尺寸:', width, 'x', height);

    // 保存引用
    sceneRef.current = scene;
    rendererRef.current = renderer;

    // 地球半径
    const EARTH_RADIUS = 2;

    // 创建地球组
    const earthGroup = new THREE.Group();
    scene.add(earthGroup);

    // 创建粒子系统组
    const particleGroup = new THREE.Group();
    scene.add(particleGroup);

    // 创建连接线组
    const connectionGroup = new THREE.Group();
    scene.add(connectionGroup);
    connectionLinesRef.current = connectionGroup;

    // 创建流动粒子组
    const flowingGroup = new THREE.Group();
    scene.add(flowingGroup);
    flowingParticlesRef.current = flowingGroup;

    // 获取CSS变量颜色
    const getThemeColor = (variable: string) => {
      try {
        const color = getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
        console.log(`获取颜色 ${variable}:`, color);

        // 如果颜色为空或无效，使用默认颜色
        if (!color || color === '') {
          console.warn(`颜色变量 ${variable} 为空，使用默认颜色`);
          return variable === '--color-primary' ? '#007aff' : '#5856d6';
        }

        return color;
      } catch (error) {
        console.error(`获取颜色变量 ${variable} 失败:`, error);
        return variable === '--color-primary' ? '#007aff' : '#5856d6';
      }
    };

    // 监听主题变化
    const updateColors = () => {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');

      // 更新线条材质颜色
      earthGroup.children.forEach(child => {
        if (child instanceof THREE.Line) {
          (child.material as THREE.LineBasicMaterial).color.set(primaryColor);
        }
      });


    };

    // 将经纬度转换为3D坐标
    function latLngToVector3(lat: number, lng: number, radius = EARTH_RADIUS) {
      const phi = (90 - lat) * (Math.PI / 180);
      const theta = (lng + 180) * (Math.PI / 180);

      const x = -(radius * Math.sin(phi) * Math.cos(theta));
      const z = (radius * Math.sin(phi) * Math.sin(theta));
      const y = (radius * Math.cos(phi));

      return new THREE.Vector3(x, y, z);
    }

    // 从坐标数组创建线条几何体
    function createLineFromCoordinates(coordinates: number[][]) {
      const points: THREE.Vector3[] = [];
      coordinates.forEach(coord => {
        if (coord.length >= 2) {
          const [lng, lat] = coord;
          points.push(latLngToVector3(lat, lng));
        }
      });
      return new THREE.BufferGeometry().setFromPoints(points);
    }

    // 处理GeoJSON数据并创建线框
    function processGeoJSON(geoData: any) {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');
      
      const lineMaterial = new THREE.LineBasicMaterial({
        color: new THREE.Color(primaryColor),
        transparent: true,
        opacity: 0.8,
      });

      geoData.features.forEach((feature: any) => {
        if (feature.geometry) {
          const { type, coordinates } = feature.geometry;

          if (type === 'Polygon') {
            coordinates.forEach((ring: number[][]) => {
              const geometry = createLineFromCoordinates(ring);
              const line = new THREE.Line(geometry, lineMaterial);
              earthGroup.add(line);
            });
          } else if (type === 'MultiPolygon') {
            coordinates.forEach((polygon: number[][][]) => {
              polygon.forEach((ring: number[][]) => {
                const geometry = createLineFromCoordinates(ring);
                const line = new THREE.Line(geometry, lineMaterial);
                earthGroup.add(line);
              });
            });
          }
        }
      });
    }



    // 加载世界地理数据
    async function loadWorldData() {
      console.log('正在加载世界地理数据...');
      const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoData = await response.json();
      console.log('地理数据加载成功，包含', geoData.features.length, '个国家/地区');

      // 添加真实的地理数据
      processGeoJSON(geoData);
      console.log('真实地球线框渲染完成');
    }

    // 备选方案：创建简单的球体线框
    function createFallbackSphere() {
      console.log('使用备选球体线框');
      const sphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS, 32, 16);
      const sphereMaterial = new THREE.MeshBasicMaterial({
        color: new THREE.Color(getThemeColor('--color-primary')),
        wireframe: true,
        transparent: true,
        opacity: 0.8
      });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      earthGroup.add(sphere);
      console.log('备选球体添加完成');
    }

    // 创建地理数据粒子系统 - 模拟地理计算中的数据点分布
    function createDataParticles() {
      // 根据设备性能调整粒子数量，确保流畅运行
      const isMobile = window.innerWidth < 768;
      const isLowEnd = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4;
      const particleCount = isMobile || isLowEnd ? 100 : 150; // 优化后的数据点数量

      const positions = new Float32Array(particleCount * 3);
      const colors = new Float32Array(particleCount * 3);
      const sizes = new Float32Array(particleCount);

      // 获取主题色彩，用于表示不同类型的地理数据
      const primaryColor = new THREE.Color(getThemeColor('--color-primary'));
      const secondaryColor = new THREE.Color(getThemeColor('--color-secondary'));
      const successColor = new THREE.Color(getThemeColor('--color-success'));

      // 在地球表面随机分布数据点，模拟城市、经济中心等地理要素
      for (let i = 0; i < particleCount; i++) {
        // 使用球面坐标系统生成均匀分布的点
        const phi = Math.random() * Math.PI * 2; // 经度 (0 到 2π)
        const theta = Math.acos(Math.random() * 2 - 1); // 纬度，使用反余弦确保均匀分布
        const radius = EARTH_RADIUS + 0.1 + Math.random() * 0.2; // 略高于地球表面，模拟数据层

        // 球面坐标转换为笛卡尔坐标
        const x = radius * Math.sin(theta) * Math.cos(phi);
        const y = radius * Math.cos(theta);
        const z = radius * Math.sin(theta) * Math.sin(phi);

        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;

        // 根据地理计算主题分配颜色：蓝色(空间分析)、紫色(城市计算)、绿色(经济建模)
        const colorChoice = Math.random();
        let color;
        if (colorChoice < 0.4) {
          color = primaryColor; // 主要用于空间分析数据点
        } else if (colorChoice < 0.7) {
          color = secondaryColor; // 用于城市计算数据点
        } else {
          color = successColor; // 用于经济建模数据点
        }

        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;

        // 根据数据重要性设置不同大小
        sizes[i] = 1.5 + Math.random() * 2.5; // 减小粒子大小以提升性能
      }

      const geometry = new THREE.BufferGeometry();
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

      // 创建高性能粒子材质，使用着色器实现动态效果
      const material = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          pixelRatio: { value: Math.min(window.devicePixelRatio, 2) } // 限制像素比以优化性能
        },
        vertexShader: `
          attribute float size;
          attribute vec3 color;
          varying vec3 vColor;
          varying float vAlpha;
          uniform float time;

          void main() {
            vColor = color;

            // 添加数据流动的呼吸效果，模拟实时数据更新
            float pulse = sin(time * 1.5 + position.x * 0.3 + position.z * 0.2) * 0.25 + 0.75;
            vAlpha = pulse;

            // 计算粒子大小，距离越远越小（透视效果）
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            gl_PointSize = size * pulse * (250.0 / -mvPosition.z); // 优化大小计算
            gl_Position = projectionMatrix * mvPosition;
          }
        `,
        fragmentShader: `
          varying vec3 vColor;
          varying float vAlpha;

          void main() {
            // 创建圆形粒子，模拟数据点
            float distance = length(gl_PointCoord - vec2(0.5));
            if (distance > 0.5) discard;

            // 添加柔和的光晕效果，增强科技感
            float alpha = (1.0 - distance * 2.0) * vAlpha * 0.8; // 降低透明度以减少过度曝光
            gl_FragColor = vec4(vColor, alpha);
          }
        `,
        transparent: true,
        vertexColors: true,
        blending: THREE.AdditiveBlending,
        depthWrite: false // 禁用深度写入以提升性能
      });

      const particles = new THREE.Points(geometry, material);
      particleGroup.add(particles);
      particleSystemRef.current = particles;

      console.log('数据粒子系统创建完成，包含', particleCount, '个粒子');
    }



    // 创建连接线系统 - 模拟地理网络和数据流连接
    function createConnectionLines() {
      // 根据设备性能调整连接线数量
      const isMobile = window.innerWidth < 768;
      const lineCount = isMobile ? 25 : 35; // 优化连接线数量
      const primaryColor = new THREE.Color(getThemeColor('--color-primary'));

      for (let i = 0; i < lineCount; i++) {
        // 在球面上生成两个随机点，模拟城市间或数据节点间的连接
        const phi1 = Math.random() * Math.PI * 2;
        const theta1 = Math.acos(Math.random() * 2 - 1);
        const phi2 = Math.random() * Math.PI * 2;
        const theta2 = Math.acos(Math.random() * 2 - 1);

        const radius = EARTH_RADIUS + 0.15; // 稍微高于地球表面

        const point1 = new THREE.Vector3(
          radius * Math.sin(theta1) * Math.cos(phi1),
          radius * Math.cos(theta1),
          radius * Math.sin(theta1) * Math.sin(phi1)
        );

        const point2 = new THREE.Vector3(
          radius * Math.sin(theta2) * Math.cos(phi2),
          radius * Math.cos(theta2),
          radius * Math.sin(theta2) * Math.sin(phi2)
        );

        // 只连接距离适中的点，模拟实际的地理网络连接
        const distance = point1.distanceTo(point2);
        if (distance > 1.2 && distance < 2.8) { // 调整距离范围以获得更好的视觉效果
          const geometry = new THREE.BufferGeometry().setFromPoints([point1, point2]);
          const material = new THREE.LineBasicMaterial({
            color: primaryColor,
            transparent: true,
            opacity: 0.25, // 降低初始透明度
            linewidth: 1
          });

          const line = new THREE.Line(geometry, material);
          connectionGroup.add(line);
        }
      }

      console.log('地理网络连接线系统创建完成，共', connectionGroup.children.length, '条连接');
    }

    // 创建流动粒子效果 - 模拟数据流和信息传输
    function createFlowingParticles() {
      // 根据设备性能调整流动线数量
      const isMobile = window.innerWidth < 768;
      const flowCount = isMobile ? 15 : 20; // 优化流动线数量
      const successColor = new THREE.Color(getThemeColor('--color-success'));

      for (let i = 0; i < flowCount; i++) {
        // 随机选择创建纬线或经线，模拟全球数据流动路径
        const isLatitudeLine = Math.random() > 0.5;
        const points: THREE.Vector3[] = [];

        if (isLatitudeLine) {
          // 纬线 - 模拟东西向数据流
          const lat = (Math.random() - 0.5) * Math.PI * 0.7; // 避免极点，集中在主要活动区域
          const segmentCount = isMobile ? 24 : 32; // 移动端减少点数
          for (let j = 0; j <= segmentCount; j++) {
            const lng = (j / segmentCount) * Math.PI * 2;
            const radius = EARTH_RADIUS + 0.12;
            points.push(latLngToVector3(lat * 180 / Math.PI, lng * 180 / Math.PI - 180, radius));
          }
        } else {
          // 经线 - 模拟南北向数据流
          const lng = Math.random() * Math.PI * 2;
          const segmentCount = isMobile ? 24 : 32;
          for (let j = 0; j <= segmentCount; j++) {
            const lat = ((j / segmentCount) - 0.5) * Math.PI * 0.7;
            const radius = EARTH_RADIUS + 0.12;
            points.push(latLngToVector3(lat * 180 / Math.PI, lng * 180 / Math.PI - 180, radius));
          }
        }

        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({
          color: successColor,
          transparent: true,
          opacity: 0.4, // 降低初始透明度
          linewidth: 1 // 减小线宽以提升性能
        });

        const line = new THREE.Line(geometry, material);
        line.userData = {
          isFlowing: true,
          flowSpeed: 0.008 + Math.random() * 0.015, // 稍微减慢流动速度
          originalOpacity: 0.4
        };
        flowingGroup.add(line);
      }

      console.log('数据流动效果创建完成，共', flowingGroup.children.length, '条流动路径');
    }

    // 设置相机位置
    camera.position.set(0, 0, 6);
    camera.lookAt(0, 0, 0);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1.0);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // 主动画循环 - 控制所有视觉效果的更新
    function animate() {
      animationIdRef.current = requestAnimationFrame(animate);
      const time = Date.now() * 0.001; // 获取当前时间，用于所有动画计算

      // 缓慢旋转地球，模拟地球自转
      earthGroup.rotation.y += 0.0015; // 进一步优化旋转速度
      earthGroup.rotation.x += 0.0003; // 轻微的倾斜旋转

      // 更新数据粒子的呼吸动画
      if (particleSystemRef.current) {
        const material = particleSystemRef.current.material as THREE.ShaderMaterial;
        if (material.uniforms && material.uniforms.time) {
          material.uniforms.time.value = time;
        }
      }

      // 更新网络连接线的闪烁效果，模拟数据传输
      if (connectionLinesRef.current) {
        connectionLinesRef.current.children.forEach((line, index) => {
          const material = (line as THREE.Line).material as THREE.LineBasicMaterial;
          const phase = time * 1.5 + index * 0.3; // 减慢闪烁频率
          material.opacity = 0.08 + Math.sin(phase) * 0.15; // 降低闪烁强度
        });
      }

      // 更新数据流动效果，模拟实时信息传输
      if (flowingParticlesRef.current) {
        flowingParticlesRef.current.children.forEach((line, index) => {
          const material = (line as THREE.Line).material as THREE.LineBasicMaterial;
          const userData = line.userData;
          if (userData.isFlowing) {
            const phase = time * userData.flowSpeed + index * 0.5;
            const opacity = userData.originalOpacity * (0.4 + Math.sin(phase) * 0.6);
            material.opacity = Math.max(0.05, opacity); // 确保最小可见度
          }
        });
      }

      // 渲染场景
      renderer.render(scene, camera);
    }

    // 添加调试信息
    console.log('场景对象数量:', scene.children.length);
    console.log('地球组对象数量:', earthGroup.children.length);

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = containerRect.width || window.innerWidth;
      const newHeight = containerRect.height || window.innerHeight;

      camera.aspect = newWidth / newHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(newWidth, newHeight);
      console.log('窗口大小调整:', newWidth, 'x', newHeight);
    };

    window.addEventListener('resize', handleResize);

    // 监听主题变化
    let observer: MutationObserver | null = null;

    const initThemeObserver = () => {
      observer = new MutationObserver(() => {
        updateColors();
      });
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
      });
    };

    // 开始动画
    animate();

    // 初始化主题监听器
    initThemeObserver();

    // 尝试加载真实地理数据，失败时使用备选球体
    loadWorldData().then(() => {
      console.log('真实地理数据加载并渲染完成');
      console.log('当前地球组包含对象数量:', earthGroup.children.length);

      // 创建粒子系统效果
      createDataParticles();
      createConnectionLines();
      createFlowingParticles();
      console.log('所有粒子系统效果创建完成');
    }).catch((error) => {
      console.log('地理数据加载失败，使用备选球体:', error.message);
      createFallbackSphere();
      console.log('备选球体创建完成，地球组包含对象数量:', earthGroup.children.length);

      // 即使使用备选球体，也创建粒子系统效果
      createDataParticles();
      createConnectionLines();
      createFlowingParticles();
      console.log('备选模式下粒子系统效果创建完成');
    });

    // 组件清理函数 - 释放所有THREE.js资源和事件监听器
    return () => {
      mountedRef.current = false;
      console.log('开始清理CountryOutlineBackground组件资源...');

      // 移除事件监听器
      window.removeEventListener('resize', handleResize);
      if (observer) {
        observer.disconnect();
      }

      // 停止动画循环
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      // 清理粒子系统资源
      if (particleSystemRef.current) {
        const material = particleSystemRef.current.material as THREE.ShaderMaterial;
        if (material) {
          material.dispose();
        }
        const geometry = particleSystemRef.current.geometry;
        if (geometry) {
          geometry.dispose();
        }
        particleSystemRef.current = null;
      }

      // 清理连接线和流动粒子资源
      [connectionLinesRef.current, flowingParticlesRef.current].forEach(group => {
        if (group) {
          group.children.forEach(child => {
            if (child instanceof THREE.Line) {
              const material = child.material as THREE.LineBasicMaterial;
              const geometry = child.geometry;
              if (material) material.dispose();
              if (geometry) geometry.dispose();
            }
          });
        }
      });

      // 清理渲染器和DOM元素
      if (rendererRef.current && containerRef.current && containerRef.current.contains(rendererRef.current.domElement)) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
        rendererRef.current = null;
      }

      // 清理场景
      if (sceneRef.current) {
        sceneRef.current.clear();
        sceneRef.current = null;
      }

      // 重置引用
      connectionLinesRef.current = null;
      flowingParticlesRef.current = null;

      console.log('CountryOutlineBackground: 所有资源清理完成');
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2, // 提高z-index，但仍低于文字内容(z-index: 10)
        pointerEvents: 'none',
        overflow: 'hidden'
      }}
    />
  );
};

export default CountryOutlineBackground;

import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';

const CountryOutlineBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const mountedRef = useRef<boolean>(false);

  // 粒子系统相关引用
  const particleSystemRef = useRef<THREE.Points | null>(null);

  useEffect(() => {
    if (!containerRef.current || mountedRef.current) return;

    mountedRef.current = true;
    console.log('CountryOutlineBackground: 开始初始化');

    // 检查THREE.js是否可用
    if (typeof THREE === 'undefined') {
      console.error('THREE.js library is not loaded');
      return;
    }

    console.log('THREE.js 版本:', THREE.REVISION);

    // 获取容器尺寸
    const containerRect = containerRef.current.getBoundingClientRect();
    const width = containerRect.width || window.innerWidth;
    const height = containerRect.height || window.innerHeight;

    // 场景设置
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      premultipliedAlpha: false
    });

    // 配置渲染器
    renderer.setSize(width, height);
    renderer.setClearColor(0x000000, 0); // 完全透明背景
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比以提高性能

    // 设置canvas样式
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.pointerEvents = 'none';
    renderer.domElement.style.zIndex = '1';

    containerRef.current.appendChild(renderer.domElement);
    console.log('渲染器初始化完成, 尺寸:', width, 'x', height);

    // 保存引用
    sceneRef.current = scene;
    rendererRef.current = renderer;

    // 地球半径
    const EARTH_RADIUS = 2;

    // 创建地球组
    const earthGroup = new THREE.Group();
    scene.add(earthGroup);

    // 创建粒子系统组
    const particleGroup = new THREE.Group();
    scene.add(particleGroup);

    // 获取CSS变量颜色
    const getThemeColor = (variable: string) => {
      try {
        const color = getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
        console.log(`获取颜色 ${variable}:`, color);

        // 如果颜色为空或无效，使用默认颜色
        if (!color || color === '') {
          console.warn(`颜色变量 ${variable} 为空，使用默认颜色`);
          return variable === '--color-primary' ? '#007aff' : '#5856d6';
        }

        return color;
      } catch (error) {
        console.error(`获取颜色变量 ${variable} 失败:`, error);
        return variable === '--color-primary' ? '#007aff' : '#5856d6';
      }
    };

    // 监听主题变化
    const updateColors = () => {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');

      // 更新线条材质颜色
      earthGroup.children.forEach(child => {
        if (child instanceof THREE.Line) {
          (child.material as THREE.LineBasicMaterial).color.set(primaryColor);
        }
      });


    };

    // 将经纬度转换为3D坐标
    function latLngToVector3(lat: number, lng: number, radius = EARTH_RADIUS) {
      const phi = (90 - lat) * (Math.PI / 180);
      const theta = (lng + 180) * (Math.PI / 180);

      const x = -(radius * Math.sin(phi) * Math.cos(theta));
      const z = (radius * Math.sin(phi) * Math.sin(theta));
      const y = (radius * Math.cos(phi));

      return new THREE.Vector3(x, y, z);
    }

    // 从坐标数组创建线条几何体
    function createLineFromCoordinates(coordinates: number[][]) {
      const points: THREE.Vector3[] = [];
      coordinates.forEach(coord => {
        if (coord.length >= 2) {
          const [lng, lat] = coord;
          points.push(latLngToVector3(lat, lng));
        }
      });
      return new THREE.BufferGeometry().setFromPoints(points);
    }

    // 处理GeoJSON数据并创建线框
    function processGeoJSON(geoData: any) {
      const primaryColor = getThemeColor('--color-primary');
      const secondaryColor = getThemeColor('--color-secondary');
      
      const lineMaterial = new THREE.LineBasicMaterial({
        color: new THREE.Color(primaryColor),
        transparent: true,
        opacity: 0.8,
      });

      geoData.features.forEach((feature: any) => {
        if (feature.geometry) {
          const { type, coordinates } = feature.geometry;

          if (type === 'Polygon') {
            coordinates.forEach((ring: number[][]) => {
              const geometry = createLineFromCoordinates(ring);
              const line = new THREE.Line(geometry, lineMaterial);
              earthGroup.add(line);
            });
          } else if (type === 'MultiPolygon') {
            coordinates.forEach((polygon: number[][][]) => {
              polygon.forEach((ring: number[][]) => {
                const geometry = createLineFromCoordinates(ring);
                const line = new THREE.Line(geometry, lineMaterial);
                earthGroup.add(line);
              });
            });
          }
        }
      });
    }



    // 加载世界地理数据
    async function loadWorldData() {
      console.log('正在加载世界地理数据...');
      const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoData = await response.json();
      console.log('地理数据加载成功，包含', geoData.features.length, '个国家/地区');

      // 添加真实的地理数据
      processGeoJSON(geoData);
      console.log('真实地球线框渲染完成');
    }

    // 备选方案：创建简单的球体线框
    function createFallbackSphere() {
      console.log('使用备选球体线框');
      const sphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS, 32, 16);
      const sphereMaterial = new THREE.MeshBasicMaterial({
        color: new THREE.Color(getThemeColor('--color-primary')),
        wireframe: true,
        transparent: true,
        opacity: 0.8
      });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      earthGroup.add(sphere);
      console.log('备选球体添加完成');
    }

    // 创建精致的数据点粒子系统 - 模拟地理计算中的关键数据节点
    function createDataParticles() {
      const particleCount = 10; // 进一步精简数量，确保精致效果

      const positions = new Float32Array(particleCount * 3);
      const colors = new Float32Array(particleCount * 3);

      // 获取主题色彩，用于表示不同类型的地理数据
      const primaryColor = new THREE.Color(getThemeColor('--color-primary'));
      const secondaryColor = new THREE.Color(getThemeColor('--color-secondary'));
      const successColor = new THREE.Color(getThemeColor('--color-success'));

      // 在地球表面精心分布少量数据点，模拟重要的地理节点
      for (let i = 0; i < particleCount; i++) {
        // 使用改进的球面坐标系统，避免极点聚集
        const phi = Math.random() * Math.PI * 2; // 经度 (0 到 2π)
        const u = Math.random(); // 均匀分布的随机数
        const theta = Math.acos(2 * u - 1); // 改进的纬度分布，避免极点过密
        const radius = EARTH_RADIUS + 0.02; // 更贴近地球表面

        // 球面坐标转换为笛卡尔坐标
        const x = radius * Math.sin(theta) * Math.cos(phi);
        const y = radius * Math.cos(theta);
        const z = radius * Math.sin(theta) * Math.sin(phi);

        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;

        // 根据地理计算主题分配颜色：蓝色(空间分析)、紫色(城市计算)、绿色(经济建模)
        const colorChoice = Math.random();
        let color;
        if (colorChoice < 0.4) {
          color = primaryColor; // 主要用于空间分析数据点
        } else if (colorChoice < 0.7) {
          color = secondaryColor; // 用于城市计算数据点
        } else {
          color = successColor; // 用于经济建模数据点
        }

        colors[i * 3] = color.r;
        colors[i * 3 + 1] = color.g;
        colors[i * 3 + 2] = color.b;
      }

      const geometry = new THREE.BufferGeometry();
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

      // 创建高质量圆形纹理，确保粒子显示为精致圆点
      const canvas = document.createElement('canvas');
      canvas.width = 64; // 提高分辨率以获得更清晰的圆形
      canvas.height = 64;
      const context = canvas.getContext('2d')!;

      // 清除画布
      context.clearRect(0, 0, 64, 64);

      // 绘制精致的圆形渐变，中心亮边缘柔和透明
      const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
      gradient.addColorStop(0, 'rgba(255, 255, 255, 1)'); // 中心完全不透明
      gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.9)'); // 内圈高亮
      gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.4)'); // 中圈渐变
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)'); // 边缘完全透明

      context.fillStyle = gradient;
      context.fillRect(0, 0, 64, 64);

      const texture = new THREE.CanvasTexture(canvas);
      texture.generateMipmaps = false; // 禁用mipmap以保持清晰度
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;

      // 创建精致的圆形粒子材质
      const material = new THREE.PointsMaterial({
        size: 2.5, // 更小的尺寸，确保精致效果
        map: texture, // 使用高质量圆形纹理
        vertexColors: true,
        transparent: true,
        opacity: 0.9, // 稍高的透明度确保可见性
        blending: THREE.AdditiveBlending,
        depthWrite: false,
        sizeAttenuation: true, // 启用距离衰减
        alphaTest: 0.01 // 更低的阈值保持柔和边缘
      });

      const particles = new THREE.Points(geometry, material);
      // 将粒子添加到地球组，使其跟随地球旋转
      earthGroup.add(particles);
      particleSystemRef.current = particles;

      console.log('精致圆形数据点系统创建完成，包含', particleCount, '个小圆点光效');
    }







    // 设置相机位置
    camera.position.set(0, 0, 6);
    camera.lookAt(0, 0, 0);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1.0);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // 主动画循环 - 控制所有视觉效果的更新
    function animate() {
      animationIdRef.current = requestAnimationFrame(animate);
      const time = Date.now() * 0.001; // 获取当前时间，用于所有动画计算

      // 缓慢旋转地球，模拟地球自转
      earthGroup.rotation.y += 0.0015; // 进一步优化旋转速度
      earthGroup.rotation.x += 0.0003; // 轻微的倾斜旋转

      // 粒子系统现在跟随地球旋转，无需额外动画更新

      // 渲染场景
      renderer.render(scene, camera);
    }

    // 添加调试信息
    console.log('场景对象数量:', scene.children.length);
    console.log('地球组对象数量:', earthGroup.children.length);

    // 处理窗口大小变化
    const handleResize = () => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newWidth = containerRect.width || window.innerWidth;
      const newHeight = containerRect.height || window.innerHeight;

      camera.aspect = newWidth / newHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(newWidth, newHeight);
      console.log('窗口大小调整:', newWidth, 'x', newHeight);
    };

    window.addEventListener('resize', handleResize);

    // 监听主题变化
    let observer: MutationObserver | null = null;

    const initThemeObserver = () => {
      observer = new MutationObserver(() => {
        updateColors();
      });
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
      });
    };

    // 开始动画
    animate();

    // 初始化主题监听器
    initThemeObserver();

    // 尝试加载真实地理数据，失败时使用备选球体
    loadWorldData().then(() => {
      console.log('真实地理数据加载并渲染完成');
      console.log('当前地球组包含对象数量:', earthGroup.children.length);

      // 创建粒子系统效果
      createDataParticles();
      console.log('精致粒子系统效果创建完成');
    }).catch((error) => {
      console.log('地理数据加载失败，使用备选球体:', error.message);
      createFallbackSphere();
      console.log('备选球体创建完成，地球组包含对象数量:', earthGroup.children.length);

      // 即使使用备选球体，也创建粒子系统效果
      createDataParticles();
      console.log('备选模式下精致粒子系统效果创建完成');
    });

    // 组件清理函数 - 释放所有THREE.js资源和事件监听器
    return () => {
      mountedRef.current = false;
      console.log('开始清理CountryOutlineBackground组件资源...');

      // 移除事件监听器
      window.removeEventListener('resize', handleResize);
      if (observer) {
        observer.disconnect();
      }

      // 停止动画循环
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      // 清理粒子系统资源
      if (particleSystemRef.current) {
        const material = particleSystemRef.current.material as THREE.PointsMaterial;
        if (material) {
          material.dispose();
        }
        const geometry = particleSystemRef.current.geometry;
        if (geometry) {
          geometry.dispose();
        }
        particleSystemRef.current = null;
      }

      // 清理渲染器和DOM元素
      if (rendererRef.current && containerRef.current && containerRef.current.contains(rendererRef.current.domElement)) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
        rendererRef.current = null;
      }

      // 清理场景
      if (sceneRef.current) {
        sceneRef.current.clear();
        sceneRef.current = null;
      }



      console.log('CountryOutlineBackground: 所有资源清理完成');
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2, // 提高z-index，但仍低于文字内容(z-index: 10)
        pointerEvents: 'none',
        overflow: 'hidden'
      }}
    />
  );
};

export default CountryOutlineBackground;

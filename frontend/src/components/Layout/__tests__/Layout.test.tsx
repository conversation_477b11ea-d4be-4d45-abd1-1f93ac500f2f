import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '../../../contexts/theme';
import Layout from '../index';

// Mock组件
const MockLayout = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <ThemeProvider>
      <Layout>{children}</Layout>
    </ThemeProvider>
  </BrowserRouter>
);

describe('Layout Component', () => {
  test('应该只渲染一个主题切换按钮在桌面端', () => {
    // 模拟桌面端屏幕尺寸
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    render(
      <MockLayout>
        <div>Test Content</div>
      </MockLayout>
    );

    // 查找所有主题切换按钮
    const themeButtons = screen.getAllByLabelText(/切换到.*模式/);
    
    // 应该只有一个主题切换按钮可见
    expect(themeButtons).toHaveLength(2); // 实际有两个，但一个被CSS隐藏
    
    // 检查桌面端按钮容器是否存在
    const desktopActions = document.querySelector('.desktop-actions');
    expect(desktopActions).toBeInTheDocument();
    
    // 检查移动端按钮容器是否存在
    const mobileActions = document.querySelector('.mobile-actions');
    expect(mobileActions).toBeInTheDocument();
  });

  test('应该包含正确的CSS类名用于响应式控制', () => {
    render(
      <MockLayout>
        <div>Test Content</div>
      </MockLayout>
    );

    // 检查桌面端菜单
    const desktopMenu = document.querySelector('.desktop-menu');
    expect(desktopMenu).toBeInTheDocument();

    // 检查桌面端操作区域
    const desktopActions = document.querySelector('.desktop-actions');
    expect(desktopActions).toBeInTheDocument();

    // 检查移动端操作区域
    const mobileActions = document.querySelector('.mobile-actions');
    expect(mobileActions).toBeInTheDocument();
  });

  test('应该正确渲染导航菜单项', () => {
    render(
      <MockLayout>
        <div>Test Content</div>
      </MockLayout>
    );

    // 检查主要导航链接
    expect(screen.getByText('首页')).toBeInTheDocument();
    expect(screen.getByText('团队成员')).toBeInTheDocument();
    expect(screen.getByText('研究方向')).toBeInTheDocument();
    expect(screen.getByText('科研项目')).toBeInTheDocument();
    expect(screen.getByText('学术成果')).toBeInTheDocument();
    expect(screen.getByText('新闻动态')).toBeInTheDocument();
    expect(screen.getByText('联系我们')).toBeInTheDocument();
  });

  test('应该正确渲染Logo', () => {
    render(
      <MockLayout>
        <div>Test Content</div>
      </MockLayout>
    );

    expect(screen.getByText('课题组网站')).toBeInTheDocument();
  });
});

import { useEffect, useRef, useState } from 'react';

interface UseScrollSnapOptions {
  threshold?: number;
  duration?: number;
}

export const useScrollSnap = (options: UseScrollSnapOptions = {}) => {
  const { threshold = 50, duration = 800 } = options;
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  
  // 注册分段元素
  const registerSection = (element: HTMLDivElement | null, index: number) => {
    if (element) {
      sectionsRef.current[index] = element;
    }
  };
  
  // 平滑滚动到指定分段
  const scrollToSection = (index: number) => {
    if (isScrolling || !sectionsRef.current[index] || index < 0 || index >= sectionsRef.current.length) {
      return;
    }

    setIsScrolling(true);
    setCurrentSection(index);

    const targetElement = sectionsRef.current[index];
    const targetPosition = targetElement.offsetTop;

    // 使用平滑滚动，确保精确定位到section顶部
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });

    // 滚动完成后重置状态，时间稍长确保滚动完成
    setTimeout(() => {
      setIsScrolling(false);
      // 二次校正，确保精确定位
      const currentPosition = window.pageYOffset;
      const expectedPosition = sectionsRef.current[index]?.offsetTop || 0;
      if (Math.abs(currentPosition - expectedPosition) > 10) {
        window.scrollTo({
          top: expectedPosition,
          behavior: 'auto' // 使用瞬时滚动进行微调
        });
      }
    }, duration + 100);
  };
  
  // 处理滚轮事件
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let lastWheelTime = 0;

    const handleWheel = (event: WheelEvent) => {
      // 防止在滚动过程中触发新的滚动
      if (isScrolling) {
        event.preventDefault();
        return;
      }

      const now = Date.now();
      // 防抖：限制滚动频率
      if (now - lastWheelTime < 100) {
        event.preventDefault();
        return;
      }
      lastWheelTime = now;

      // 清除之前的超时
      clearTimeout(timeoutId);

      // 只有滚动幅度足够大才触发section切换
      if (Math.abs(event.deltaY) < threshold) {
        return;
      }

      event.preventDefault();

      const sections = sectionsRef.current;
      if (sections.length === 0) return;

      // 根据滚动方向决定目标分段
      let targetSection = currentSection;
      if (event.deltaY > 0) {
        // 向下滚动
        targetSection = Math.min(currentSection + 1, sections.length - 1);
      } else {
        // 向上滚动
        targetSection = Math.max(currentSection - 1, 0);
      }

      // 只有目标分段不同时才滚动
      if (targetSection !== currentSection) {
        scrollToSection(targetSection);
      }
    };

    // 添加事件监听器
    window.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      window.removeEventListener('wheel', handleWheel);
      clearTimeout(timeoutId);
    };
  }, [currentSection, isScrolling, threshold, scrollToSection]);
  
  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (isScrolling) return;
      
      switch (event.key) {
        case 'ArrowDown':
        case 'PageDown':
          event.preventDefault();
          scrollToSection(Math.min(currentSection + 1, sectionsRef.current.length - 1));
          break;
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault();
          scrollToSection(Math.max(currentSection - 1, 0));
          break;
        case 'Home':
          event.preventDefault();
          scrollToSection(0);
          break;
        case 'End':
          event.preventDefault();
          scrollToSection(sectionsRef.current.length - 1);
          break;
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSection, isScrolling]);
  
  // 监听滚动位置变化，更新当前section状态
  useEffect(() => {
    let ticking = false;

    const updateCurrentSection = () => {
      if (isScrolling) return; // 在程序化滚动过程中不更新

      const scrollTop = window.pageYOffset;
      const sections = sectionsRef.current;
      const windowHeight = window.innerHeight;

      let newCurrentSection = 0;
      let minDistance = Infinity;

      // 找到距离当前视口中心最近的section
      for (let i = 0; i < sections.length; i++) {
        const sectionTop = sections[i].offsetTop;
        const sectionCenter = sectionTop + windowHeight / 2;
        const distance = Math.abs(scrollTop + windowHeight / 2 - sectionCenter);

        if (distance < minDistance) {
          minDistance = distance;
          newCurrentSection = i;
        }
      }

      if (newCurrentSection !== currentSection) {
        setCurrentSection(newCurrentSection);
      }

      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateCurrentSection);
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [currentSection, isScrolling]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 重新计算当前分段位置
      const scrollTop = window.pageYOffset;
      const sections = sectionsRef.current;

      for (let i = 0; i < sections.length; i++) {
        const sectionTop = sections[i].offsetTop;
        const sectionHeight = sections[i].offsetHeight;

        if (scrollTop >= sectionTop && scrollTop < sectionTop + sectionHeight) {
          setCurrentSection(i);
          break;
        }
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // 初始化时确保滚动到第一个section
  useEffect(() => {
    const initializePosition = () => {
      if (sectionsRef.current.length > 0 && window.pageYOffset === 0) {
        // 页面加载时如果在顶部，确保正确定位到第一个section
        setTimeout(() => {
          const firstSection = sectionsRef.current[0];
          if (firstSection) {
            window.scrollTo({
              top: firstSection.offsetTop,
              behavior: 'auto'
            });
            setCurrentSection(0);
          }
        }, 100);
      }
    };

    // 页面加载完成后初始化
    if (document.readyState === 'complete') {
      initializePosition();
    } else {
      window.addEventListener('load', initializePosition);
      return () => window.removeEventListener('load', initializePosition);
    }
  }, []);

  return {
    containerRef,
    currentSection,
    isScrolling,
    registerSection,
    scrollToSection,
    totalSections: sectionsRef.current.length
  };
};
